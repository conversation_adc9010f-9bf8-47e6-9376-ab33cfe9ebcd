import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import '../../style.css';
import { Header, InputArea, TopBanner } from '../components';

import { ChatStatus, Message as SMessage } from '@the-agent/shared';
import { useLiveQuery } from 'dexie-react-hooks';
import { Modal } from 'antd';
import welcomeImg from '~/assets/imgs/welcome-miniapp.png';
import historyIcon from '~/assets/icons/history.svg';
import { ChatHandler } from '~/chat/handler';
import { db } from '~/storages/indexdb';
import { useUser } from '~/hooks/useUser';

import { useLanguage } from '../../utils/i18n';
import { MessageList } from '../components';
import { BANNER_STORAGE_KEY } from '~/configs/common';
import { useNavigate, useLocation } from 'react-router-dom';
import { MiniAppLocal as MiniApp } from '~/types/miniapp';
import {
  deleteMiniapp as deleteMiniappService,
  updateMiniapp as updateMiniappService,
  createNewMiniApp,
} from '~/services/miniapp';
import { formatDate, getAvatarColor, getInitials } from '~/utils/profile';
import MiniappConversationsList from '~/sidepanel/components/miniapp/MiniappConversationsList';

const MiniappInfoPanel = ({
  selectedMiniapp,
  onTopOffsetChange,
}: {
  selectedMiniapp: MiniApp;
  onTopOffsetChange: (offset: number) => void;
}) => {
  const { getMessage } = useLanguage();
  const [showHistoricalVersions, setShowHistoricalVersions] = useState(false);

  // measure the height of the info + optimized versions list
  const topBlockRef = useRef<HTMLDivElement | null>(null);
  const HEADER_HEIGHT = 44;

  // measure the combined height of the info section and the optimized versions list
  useEffect(() => {
    const element = topBlockRef.current;
    if (!element) return;

    const updateHeight = () => {
      const measured = element.getBoundingClientRect().height;
      const newTopOffset = HEADER_HEIGHT + measured;
      onTopOffsetChange(newTopOffset);
    };

    updateHeight();

    const resizeObserver = new ResizeObserver(() => updateHeight());
    resizeObserver.observe(element);
    return () => resizeObserver.disconnect();
  }, [selectedMiniapp?.id, selectedMiniapp?.history?.length, onTopOffsetChange]);

  return (
    <div
      ref={topBlockRef}
      style={{
        position: 'absolute',
        top: '44px',
        left: 0,
        right: 0,
        backgroundColor: '#ffffff',
        borderBottom: '1px solid #e5e7eb',
        zIndex: 9,
        padding: '16px',
      }}
    >
      {/* Miniapp Info Section */}
      <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
        <div
          style={{
            width: '48px',
            height: '48px',
            borderRadius: '12px',
            backgroundColor: getAvatarColor(selectedMiniapp.name),
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: '#ffffff',
            fontSize: '14px',
            fontWeight: 600,
            flexShrink: 0,
          }}
        >
          {getInitials(selectedMiniapp.name)}
        </div>
        <div style={{ flex: 1, minWidth: 0 }}>
          <div
            style={{
              fontSize: '16px',
              fontWeight: 600,
              color: '#111827',
              marginBottom: '2px',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {selectedMiniapp.name}
          </div>
          <div style={{ fontSize: '14px', color: '#6b7280', marginBottom: '2px' }}>
            {getMessage('miniappDetailVersion', String(selectedMiniapp?.history?.length ?? '1'))}
          </div>
          {selectedMiniapp.installation && (
            <div style={{ fontSize: '12px', color: '#6b7280' }}>
              {getMessage(
                'miniappDetailInstalledOn',
                formatDate(selectedMiniapp?.installation?.deployed_at || 0)
              )}
            </div>
          )}
        </div>
      </div>

      {/* Optimized Versions List */}
      {selectedMiniapp.installation && (
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '8px',
          }}
        >
          {/* Historical versions list - show if showHistoricalVersions is true */}
          {showHistoricalVersions &&
            selectedMiniapp?.history &&
            selectedMiniapp?.history.length > 0 && (
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '8px',
                }}
              >
                {selectedMiniapp?.history.map((version, index) => (
                  <div
                    key={index}
                    style={{
                      padding: '12px',
                      borderRadius: '8px',
                      border: '1px solid #e5e7eb',
                      backgroundColor: '#ffffff',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '12px',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                    }}
                    onMouseOver={e => {
                      e.currentTarget.style.backgroundColor = '#f9fafb';
                      e.currentTarget.style.borderColor = '#d1d5db';
                    }}
                    onMouseOut={e => {
                      e.currentTarget.style.backgroundColor = '#ffffff';
                      e.currentTarget.style.borderColor = '#e5e7eb';
                    }}
                  >
                    <div
                      style={{
                        width: '32px',
                        height: '32px',
                        borderRadius: '6px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <img
                        src={historyIcon}
                        alt="History"
                        style={{ width: '16px', height: '16px' }}
                      />
                    </div>
                    <div style={{ flex: 1 }}>
                      <div
                        style={{
                          fontSize: '14px',
                          fontWeight: 600,
                          color: '#111827',
                          marginBottom: '2px',
                        }}
                      >
                        {getMessage(
                          'miniappDetailVersion',
                          String((selectedMiniapp?.history.length ?? 0) - index)
                        )}
                      </div>
                      <div style={{ fontSize: '12px', color: '#6b7280' }}>
                        {getMessage(
                          'miniappDetailGeneratedOn',
                          formatDate(version.deployed_at ?? 0)
                        )}
                      </div>
                    </div>
                    <div style={{ color: '#6b7280' }}>
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                      >
                        <polyline points="9,18 15,12 9,6"></polyline>
                      </svg>
                    </div>
                  </div>
                ))}
              </div>
            )}

          {/* View historical versions button - Show if showHistoricalVersions is false */}
          {!showHistoricalVersions && (
            <button
              type="button"
              onClick={() => setShowHistoricalVersions(true)}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '8px 12px',
                borderRadius: '8px',
                border: '1px solid #e5e7eb',
                backgroundColor: '#ffffff',
                color: '#374151',
                fontSize: '14px',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                justifyContent: 'space-between',
                width: '100%',
              }}
              onMouseOver={e => {
                e.currentTarget.style.backgroundColor = '#f9fafb';
                e.currentTarget.style.borderColor = '#d1d5db';
              }}
              onMouseOut={e => {
                e.currentTarget.style.backgroundColor = '#ffffff';
                e.currentTarget.style.borderColor = '#e5e7eb';
              }}
            >
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <img src={historyIcon} alt="History" style={{ width: '16px', height: '16px' }} />
                <span>{getMessage('miniappDetailViewAllVersions')}</span>
              </div>
            </button>
          )}
        </div>
      )}
    </div>
  );
};

const WelcomeComponent = ({ selectedMiniapp }: { selectedMiniapp: MiniApp | null }) => {
  const { getMessage } = useLanguage();

  if (selectedMiniapp) {
    return (
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '16px 24px',
          minHeight: '200px',
        }}
      >
        <p
          style={{
            fontSize: '16px',
            color: '#6b7280',
            lineHeight: '1.6',
            marginBottom: '0',
            textAlign: 'center',
          }}
        >
          {getMessage('startTyping')}
        </p>
      </div>
    );
  }

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '0px 24px',
        minHeight: '100%',
      }}
    >
      <div
        style={{
          maxWidth: '480px',
          textAlign: 'center',
        }}
      >
        <img
          src={welcomeImg}
          alt="Welcome"
          style={{
            width: '200px',
            height: 'auto',
            opacity: 0.8,
          }}
        />
        {/* Welcome text */}
        <p
          style={{
            fontSize: '25px',
            fontWeight: '600',
            color: '#374151',
            marginBottom: '0px',
          }}
        >
          {getMessage('scriptWelcome')}
        </p>
      </div>
    </div>
  );
};

type FilterType = 'All' | 'Installed' | 'Uninstalled';

const MiniappsPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { getMessage } = useLanguage();

  const [prompt, setPrompt] = useState('');
  const [status, setStatus] = useState<ChatStatus>('idle');
  const [chatHandler, setChatHandler] = useState<ChatHandler | null>(null);

  const [hasBanner, setHasBanner] = useState(false);
  const [selectedMiniapp, setSelectedMiniapp] = useState<MiniApp | null>(null);
  const [topOffset, setTopOffset] = useState(0);

  const activeUser = useUser();

  // Handle auto-selection from navigation state or create initial miniapp
  useEffect(() => {
    const handleInitialState = async () => {
      if (!activeUser?.id) return;

      // Check if we have an auto-select miniapp from navigation state
      const autoSelectMiniapp = location.state?.autoSelectMiniapp;
      if (autoSelectMiniapp) {
        setSelectedMiniapp(autoSelectMiniapp);
        // Clear the state to prevent re-selection on re-renders
        navigate('/miniapp', { replace: true, state: {} });
        return;
      }

      // If no miniapp selected, try to get the latest one or create new
      if (!selectedMiniapp) {
        try {
          const latest = await db.getLatestConversationForMiniappType(activeUser.id, 'userScript');
          if (latest) {
            setSelectedMiniapp(latest.miniapp);
          } else {
            // No existing miniapp, create a new one
            const newMiniapp = await createNewMiniApp(activeUser.id, 'New Script', 'userScript');
            setSelectedMiniapp(newMiniapp);
          }
        } catch (error) {
          console.error('Failed to handle initial state:', error);
        }
      }
    };

    handleInitialState();
  }, [activeUser?.id, location.state, navigate, selectedMiniapp]);

  const convId = useMemo(() => {
    if (selectedMiniapp) {
      return selectedMiniapp.conversation_id;
    }
    return -1;
  }, [selectedMiniapp]);

  useEffect(() => {
    if (convId !== -1) {
      setChatHandler(
        new ChatHandler({
          currentConversationId: convId,
          miniapp: selectedMiniapp || undefined,
          setStatus,
          onMessageUpdate: async (message: SMessage) => {
            await db.saveMessage(message);
          },
        })
      );
    }
  }, [convId, setStatus, selectedMiniapp]);

  const abort = useCallback(() => {
    chatHandler?.abort();
  }, [chatHandler]);

  // Monitor TopBanner visibility state
  useEffect(() => {
    const checkBannerState = () => {
      chrome.storage.local.get([BANNER_STORAGE_KEY], result => {
        setHasBanner(!result[BANNER_STORAGE_KEY]);
      });
    };

    // Initial check
    checkBannerState();

    // Listen for storage changes
    const handleStorageChange = (changes: { [key: string]: chrome.storage.StorageChange }) => {
      if (changes[BANNER_STORAGE_KEY]) {
        setHasBanner(!changes[BANNER_STORAGE_KEY].newValue);
      }
    };

    chrome.storage.onChanged.addListener(handleStorageChange);

    return () => {
      chrome.storage.onChanged.removeListener(handleStorageChange);
    };
  }, []);

  const activeTab = 'userScript';

  const [showArchivedView, setShowArchivedView] = useState(false);
  const [confirmDelete, setConfirmDelete] = useState<number | null>(null);
  const [showMiniappApplicationsList, setShowMiniappApplicationsList] = useState(false);
  const [filter, setFilter] = useState<FilterType>('All');

  const conversationsWithMiniapps = useLiveQuery(async () => {
    if (!activeUser?.id) return [];
    try {
      return await db.getConversationsWithMiniapps(activeUser.id, 'userScript');
    } catch (error) {
      console.error('Error loading conversations with miniapps:', error);
      return [];
    }
  }, [activeUser?.id]);

  // Filter conversations by installation status if needed
  const filteredConversationsWithMiniapps = useMemo(() => {
    if (!conversationsWithMiniapps) return [];

    // Apply the installation filter
    if (filter === 'All') return conversationsWithMiniapps;
    if (filter === 'Installed')
      return conversationsWithMiniapps.filter(
        item => item.miniapp.installation && item.miniapp.installation.code
      );
    if (filter === 'Uninstalled')
      return conversationsWithMiniapps.filter(
        item => !item.miniapp.installation || !item.miniapp.installation.code
      );
    return conversationsWithMiniapps;
  }, [conversationsWithMiniapps, filter]);

  const handleSelectConversation = async (conversation: any, miniapp: MiniApp) => {
    // Update conversation's last_selected_at
    await db.updateConversation(conversation.id, { last_selected_at: Date.now() });
    setSelectedMiniapp(miniapp);
    setShowMiniappApplicationsList(false);
  };

  const handleArchiveMiniapp = async (miniapp: MiniApp) => {
    try {
      await updateMiniappService(miniapp.id, { status: 'archived' });
    } catch (error) {
      console.error('Failed to archive miniapp:', error);
    }
  };

  const handleActivateMiniapp = async (miniapp: MiniApp) => {
    try {
      await updateMiniappService(miniapp.id, { status: 'active' });
    } catch (error) {
      console.error('Failed to activate miniapp:', error);
    }
  };

  const handleDeleteMiniapp = (miniapp: MiniApp) => {
    setConfirmDelete(miniapp.id);
  };

  const handleCancelDelete = () => setConfirmDelete(null);

  const handleConfirmDelete = async () => {
    if (confirmDelete == null) return;
    try {
      await deleteMiniappService(confirmDelete);
    } catch (error) {
      console.error('Failed to delete miniapp:', error);
    } finally {
      setConfirmDelete(null);
    }
  };

  const handleToggleArchivedView = (isArchived: boolean) => {
    setShowArchivedView(isArchived);
  };

  const toggleMiniappApplicationsList = (value?: boolean) => {
    const willShow = value !== undefined ? value : !showMiniappApplicationsList;
    setShowMiniappApplicationsList(willShow);
  };

  return (
    <div
      style={{
        height: '100vh',
        width: '100%',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* Header */}
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          backgroundColor: 'white',
          zIndex: 10,
        }}
      >
        <Header
          user={activeUser}
          setShowConversationList={() => {}}
          setShowMiniappApplicationsList={toggleMiniappApplicationsList} // Not used in ConversationPage
          setShowWorkflowConversationList={() => {}}
          setShowTelegramRemoteConversation={() => {}} // Not used in ConversationPage
        />
      </div>

      {/* Top Banner */}
      <TopBanner />

      {/* Miniapp Info Panel - Fixed at top when miniapp is selected */}
      {selectedMiniapp && (
        <MiniappInfoPanel selectedMiniapp={selectedMiniapp} onTopOffsetChange={setTopOffset} />
      )}

      {/* Messages Area */}
      <MessageList
        key={convId}
        convId={convId}
        workflowMode={false}
        welcomeComponent={<WelcomeComponent selectedMiniapp={selectedMiniapp} />}
        status={status}
        top={topOffset}
        hasBanner={hasBanner}
      />

      {/* Input Area */}
      <div
        style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          backgroundColor: 'white',
          zIndex: 10,
        }}
      >
        <InputArea
          prompt={prompt}
          setPrompt={setPrompt}
          onSubmitRich={chatHandler?.handleSubmit}
          status={status}
          abort={abort}
        />
      </div>

      {/* Miniapp Applications List */}
      {showMiniappApplicationsList && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 50,
            animation: 'fadeIn 0.2s ease-out',
          }}
          onClick={() => setShowMiniappApplicationsList(false)}
        >
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '320px',
              height: '100%',
              boxShadow: '2px 0 10px rgba(0, 0, 0, 0.1)',
              animation: 'slideInFromLeft 0.3s ease-out',
              overflow: 'hidden',
            }}
            onClick={e => e.stopPropagation()}
          >
            <MiniappConversationsList
              conversationsWithMiniapps={filteredConversationsWithMiniapps || []}
              onSelectConversation={handleSelectConversation}
              onArchiveMiniapp={handleArchiveMiniapp}
              onDeleteMiniapp={handleDeleteMiniapp}
              onActivateMiniapp={handleActivateMiniapp}
              isArchivedView={showArchivedView}
              onClose={() => setShowMiniappApplicationsList(false)}
              filter={filter}
              onFilterChange={setFilter}
              activeTab={activeTab}
              onToggleArchivedView={handleToggleArchivedView}
            />
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      <Modal
        open={confirmDelete != null}
        onCancel={handleCancelDelete}
        footer={
          <div style={{ display: 'flex', justifyContent: 'center', gap: 16, marginTop: 8 }}>
            <button
              onClick={handleCancelDelete}
              style={{
                fontWeight: 500,
                fontSize: 15,
                padding: '9px 22px',
                borderRadius: 7,
                border: '1px solid #D1D5DB',
                color: '#111827',
                background: '#fff',
                cursor: 'pointer',
                transition: 'background 0.2s, border 0.2s',
              }}
            >
              {getMessage('cancel')}
            </button>
            <button
              onClick={handleConfirmDelete}
              style={{
                fontWeight: 600,
                fontSize: 15,
                padding: '9px 22px',
                borderRadius: 7,
                border: 'none',
                background: '#DC2626',
                color: '#fff',
                cursor: 'pointer',
                boxShadow: '0 2px 8px 0 rgba(220,38,38,0.08)',
                transition: 'background 0.2s',
              }}
            >
              {getMessage('delete')}
            </button>
          </div>
        }
        centered
        closable={false}
        width={300}
        styles={{
          mask: { background: 'rgba(0,0,0,0.18)' },
          content: { borderRadius: 24 },
        }}
      >
        <div style={{ textAlign: 'center' }}>
          <h3 style={{ fontSize: 22, fontWeight: 700, marginBottom: 18 }}>
            {getMessage('deleteMiniappTitle')}
          </h3>
          <div style={{ fontSize: 17, color: '#374151', marginBottom: 28 }}>
            {getMessage('deleteMiniappContent')}
          </div>
        </div>
      </Modal>
    </div>
  );
};

export { MiniappsPage };
